'use client'

import { useState, useTransition } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { validateEmail, validatePassword } from '@/lib/validations/auth'
import { Eye, EyeOff, Loader2, CheckCircle, XCircle, User, Building2 } from 'lucide-react'
import { cn } from '@/lib/utils'
import { signUpAction } from '@/app/(auth)/_actions/auth'

interface FormData {
	name: string
	email: string
	password: string
	confirmPassword: string
	role: string
	organizationName: string
	acceptTerms: boolean
	marketingEmails: boolean
}

interface FormErrors {
	name?: string
	email?: string
	password?: string
	confirmPassword?: string
	role?: string
	organizationName?: string
	acceptTerms?: string
}

export function SignUpForm() {
	const router = useRouter()
	const [isPending, startTransition] = useTransition()
	const [message, setMessage] = useState<string>('')
	const [showPassword, setShowPassword] = useState(false)
	const [showConfirmPassword, setShowConfirmPassword] = useState(false)

	const [formData, setFormData] = useState<FormData>({
		name: '',
		email: '',
		password: '',
		confirmPassword: '',
		role: '',
		organizationName: '',
		acceptTerms: false,
		marketingEmails: false
	})

	const [errors, setErrors] = useState<FormErrors>({})
	const [emailValidation, setEmailValidation] = useState({ isValid: false, error: null })
	const [passwordValidation, setPasswordValidation] = useState({ isValid: false, error: null, strength: 0 })

	const handleInputChange = (field: keyof FormData, value: string | boolean) => {
		setFormData(prev => ({ ...prev, [field]: value }))
		setErrors(prev => ({ ...prev, [field]: undefined }))

		// Real-time validation
		if (field === 'email' && typeof value === 'string') {
			const validation = validateEmail(value)
			setEmailValidation(validation)
		}

		if (field === 'password' && typeof value === 'string') {
			const validation = validatePassword(value)
			setPasswordValidation(validation)
		}

		if (field === 'confirmPassword' && typeof value === 'string') {
			if (value && value !== formData.password) {
				setErrors(prev => ({ ...prev, confirmPassword: 'Passwords do not match' }))
			} else {
				setErrors(prev => ({ ...prev, confirmPassword: undefined }))
			}
		}

		if (field === 'name' && typeof value === 'string') {
			if (value && !/^[a-zA-Z\s]+$/.test(value)) {
				setErrors(prev => ({ ...prev, name: 'Name can only contain letters and spaces' }))
			} else if (value && value.length < 2) {
				setErrors(prev => ({ ...prev, name: 'Name must be at least 2 characters' }))
			} else {
				setErrors(prev => ({ ...prev, name: undefined }))
			}
		}
	}

	const validateForm = (): boolean => {
		const newErrors: FormErrors = {}

		if (!formData.name.trim()) {
			newErrors.name = 'Name is required'
		} else if (formData.name.length < 2) {
			newErrors.name = 'Name must be at least 2 characters'
		} else if (!/^[a-zA-Z\s]+$/.test(formData.name)) {
			newErrors.name = 'Name can only contain letters and spaces'
		}

		if (!emailValidation.isValid) {
			newErrors.email = emailValidation.error || 'Please enter a valid email'
		}

		if (!passwordValidation.isValid) {
			newErrors.password = passwordValidation.error || 'Please enter a valid password'
		}

		if (formData.password !== formData.confirmPassword) {
			newErrors.confirmPassword = 'Passwords do not match'
		}

		if (!formData.role) {
			newErrors.role = 'Please select your role'
		}

		if (['broker', 'developer'].includes(formData.role) && !formData.organizationName.trim()) {
			newErrors.organizationName = 'Organization name is required for brokers and developers'
		}

		if (!formData.acceptTerms) {
			newErrors.acceptTerms = 'You must accept the terms and conditions'
		}

		setErrors(newErrors)
		return Object.keys(newErrors).length === 0
	}

	const handleSubmit = async (formDataObj: FormData) => {
		setMessage('')

		if (!validateForm()) {
			return
		}

		startTransition(async () => {
			try {
				const result = await signUpAction(formDataObj)

				if (result.error) {
					setMessage(result.error)
				} else {
					setMessage(result.message || 'Account created successfully! Redirecting...')
					setTimeout(() => {
						router.push('/dashboard')
					}, 2000)
				}
			} catch (error) {
				console.error('Signup error:', error)
				setMessage('An unexpected error occurred. Please try again.')
			}
		})
	}

	const getPasswordStrengthColor = (strength: number) => {
		if (strength < 2) return 'bg-red-500'
		if (strength < 3) return 'bg-yellow-500'
		if (strength < 4) return 'bg-blue-500'
		return 'bg-green-500'
	}

	const getPasswordStrengthText = (strength: number) => {
		if (strength < 2) return 'Weak'
		if (strength < 3) return 'Fair'
		if (strength < 4) return 'Good'
		return 'Strong'
	}

	const roleOptions = [
		{ value: 'agent', label: 'Real Estate Agent', icon: User, description: 'Individual agent selling properties' },
		{ value: 'broker', label: 'Broker/Agency', icon: Building2, description: 'Real estate brokerage or agency' },
		{ value: 'developer', label: 'Developer', icon: Building2, description: 'Property developer or builder' },
		{ value: 'buyer', label: 'Buyer', icon: User, description: 'Looking to purchase property' }
	]

	return (
		<form action={handleSubmit} className="space-y-6">
			{/* Name Field */}
			<div>
				<Label htmlFor="name" className="block text-sm font-medium text-text mb-2">
					Full Name
				</Label>
				<Input
					id="name"
					name="name"
					type="text"
					required
					value={formData.name}
					onChange={e => handleInputChange('name', e.target.value)}
					className={cn(
						errors.name && 'border-red-500 focus:ring-red-500',
						!errors.name && formData.name.length >= 2 && 'border-green-500 focus:ring-green-500'
					)}
					placeholder="Enter your full name"
				/>
				{errors.name && (
					<p className="mt-1 text-sm text-red-600 flex items-center">
						<XCircle className="w-4 h-4 mr-1" />
						{errors.name}
					</p>
				)}
			</div>

			{/* Email Field */}
			<div>
				<Label htmlFor="email" className="block text-sm font-medium text-text mb-2">
					Email Address
				</Label>
				<Input
					id="email"
					name="email"
					type="email"
					required
					value={formData.email}
					onChange={e => handleInputChange('email', e.target.value)}
					className={cn(
						(errors.email || !emailValidation.isValid) && formData.email && 'border-red-500 focus:ring-red-500',
						emailValidation.isValid && formData.email && 'border-green-500 focus:ring-green-500'
					)}
					placeholder="Enter your email address"
				/>
				{errors.email && (
					<p className="mt-1 text-sm text-red-600 flex items-center">
						<XCircle className="w-4 h-4 mr-1" />
						{errors.email}
					</p>
				)}
				{emailValidation.isValid && formData.email && (
					<p className="mt-1 text-sm text-green-600 flex items-center">
						<CheckCircle className="w-4 h-4 mr-1" />
						Valid email address
					</p>
				)}
			</div>

			{/* Password Field */}
			<div>
				<Label htmlFor="password" className="block text-sm font-medium text-text mb-2">
					Password
				</Label>
				<div className="relative">
					<Input
						id="password"
						name="password"
						type={showPassword ? 'text' : 'password'}
						required
						value={formData.password}
						onChange={e => handleInputChange('password', e.target.value)}
						className={cn(
							errors.password && 'border-red-500 focus:ring-red-500',
							passwordValidation.isValid && 'border-green-500 focus:ring-green-500'
						)}
						placeholder="Create a strong password"
					/>
					<button
						type="button"
						onClick={() => setShowPassword(!showPassword)}
						className="absolute inset-y-0 right-0 pr-3 flex items-center text-text-soft hover:text-text focus:outline-none"
					>
						{showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
					</button>
				</div>

				{/* Password Strength Indicator */}
				{formData.password && (
					<div className="mt-2">
						<div className="flex items-center justify-between text-xs text-text-soft mb-1">
							<span>Password strength</span>
							<span
								className={cn(
									'font-medium',
									passwordValidation.strength < 2 && 'text-red-600',
									passwordValidation.strength >= 2 && passwordValidation.strength < 3 && 'text-yellow-600',
									passwordValidation.strength >= 3 && passwordValidation.strength < 4 && 'text-blue-600',
									passwordValidation.strength >= 4 && 'text-green-600'
								)}
							>
								{getPasswordStrengthText(passwordValidation.strength)}
							</span>
						</div>
						<div className="w-full bg-gray-200 rounded-full h-2">
							<div
								className={cn(
									'h-2 rounded-full transition-all duration-300',
									getPasswordStrengthColor(passwordValidation.strength)
								)}
								style={{ width: `${(passwordValidation.strength / 4) * 100}%` }}
							/>
						</div>
					</div>
				)}

				{errors.password && (
					<p className="mt-1 text-sm text-red-600 flex items-center">
						<XCircle className="w-4 h-4 mr-1" />
						{errors.password}
					</p>
				)}
			</div>

			{/* Confirm Password Field */}
			<div>
				<Label htmlFor="confirmPassword" className="block text-sm font-medium text-text mb-2">
					Confirm Password
				</Label>
				<div className="relative">
					<Input
						id="confirmPassword"
						name="confirmPassword"
						type={showConfirmPassword ? 'text' : 'password'}
						required
						value={formData.confirmPassword}
						onChange={e => handleInputChange('confirmPassword', e.target.value)}
						className={cn(
							errors.confirmPassword && 'border-red-500 focus:ring-red-500',
							!errors.confirmPassword &&
								formData.confirmPassword &&
								formData.password === formData.confirmPassword &&
								'border-green-500 focus:ring-green-500'
						)}
						placeholder="Confirm your password"
					/>
					<button
						type="button"
						onClick={() => setShowConfirmPassword(!showConfirmPassword)}
						className="absolute inset-y-0 right-0 pr-3 flex items-center text-text-soft hover:text-text focus:outline-none"
					>
						{showConfirmPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
					</button>
				</div>
				{errors.confirmPassword && (
					<p className="mt-1 text-sm text-red-600 flex items-center">
						<XCircle className="w-4 h-4 mr-1" />
						{errors.confirmPassword}
					</p>
				)}
				{!errors.confirmPassword && formData.confirmPassword && formData.password === formData.confirmPassword && (
					<p className="mt-1 text-sm text-green-600 flex items-center">
						<CheckCircle className="w-4 h-4 mr-1" />
						Passwords match
					</p>
				)}
			</div>

			{/* Role Selection */}
			<div>
				<Label className="block text-sm font-medium text-text mb-3">What best describes you?</Label>
				<div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
					{roleOptions.map(option => {
						const Icon = option.icon
						return (
							<label
								key={option.value}
								className={cn(
									'relative flex items-start p-4 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors',
									formData.role === option.value ? 'border-primary bg-primary/5 ring-2 ring-primary' : 'border-border'
								)}
							>
								<input
									type="radio"
									name="role"
									value={option.value}
									checked={formData.role === option.value}
									onChange={e => handleInputChange('role', e.target.value)}
									className="sr-only"
								/>
								<Icon className="w-5 h-5 text-primary mt-0.5 mr-3 flex-shrink-0" />
								<div className="flex-1">
									<div className="text-sm font-medium text-text">{option.label}</div>
									<div className="text-xs text-text-soft mt-1">{option.description}</div>
								</div>
								{formData.role === option.value && <CheckCircle className="w-5 h-5 text-primary ml-2 flex-shrink-0" />}
							</label>
						)
					})}
				</div>
				{errors.role && (
					<p className="mt-2 text-sm text-red-600 flex items-center">
						<XCircle className="w-4 h-4 mr-1" />
						{errors.role}
					</p>
				)}
			</div>

			{/* Organization Name (conditional) */}
			{['broker', 'developer'].includes(formData.role) && (
				<div>
					<Label htmlFor="organizationName" className="block text-sm font-medium text-text mb-2">
						Organization Name
					</Label>
					<Input
						id="organizationName"
						name="organizationName"
						type="text"
						required
						value={formData.organizationName}
						onChange={e => handleInputChange('organizationName', e.target.value)}
						className={cn(
							errors.organizationName && 'border-red-500 focus:ring-red-500',
							!errors.organizationName && formData.organizationName.trim() && 'border-green-500 focus:ring-green-500'
						)}
						placeholder={formData.role === 'broker' ? 'Enter your brokerage name' : 'Enter your company name'}
					/>
					{errors.organizationName && (
						<p className="mt-1 text-sm text-red-600 flex items-center">
							<XCircle className="w-4 h-4 mr-1" />
							{errors.organizationName}
						</p>
					)}
				</div>
			)}

			{/* Terms and Conditions */}
			<div className="space-y-4">
				<div className="flex items-start">
					<input
						id="acceptTerms"
						name="acceptTerms"
						type="checkbox"
						checked={formData.acceptTerms}
						onChange={e => handleInputChange('acceptTerms', e.target.checked)}
						className={cn(
							'mt-1 h-4 w-4 rounded border-border text-primary focus:ring-primary',
							errors.acceptTerms && 'border-red-500'
						)}
					/>
					<Label htmlFor="acceptTerms" className="ml-3 text-sm text-text">
						I agree to the{' '}
						<a
							href="/terms"
							className="text-primary hover:text-primary-hover underline"
							target="_blank"
							rel="noopener noreferrer"
						>
							Terms of Service
						</a>{' '}
						and{' '}
						<a
							href="/privacy"
							className="text-primary hover:text-primary-hover underline"
							target="_blank"
							rel="noopener noreferrer"
						>
							Privacy Policy
						</a>
					</Label>
				</div>
				{errors.acceptTerms && (
					<p className="text-sm text-red-600 flex items-center">
						<XCircle className="w-4 h-4 mr-1" />
						{errors.acceptTerms}
					</p>
				)}

				<div className="flex items-start">
					<input
						id="marketingEmails"
						name="marketingEmails"
						type="checkbox"
						checked={formData.marketingEmails}
						onChange={e => handleInputChange('marketingEmails', e.target.checked)}
						className="mt-1 h-4 w-4 rounded border-border text-primary focus:ring-primary"
					/>
					<Label htmlFor="marketingEmails" className="ml-3 text-sm text-text-soft">
						I'd like to receive product updates, tips, and special offers via email (optional)
					</Label>
				</div>
			</div>

			{/* Error/Success Message */}
			{message && (
				<div
					className={cn(
						'p-4 rounded-lg text-sm',
						message.includes('successfully') || message.includes('Welcome')
							? 'bg-green-50 text-green-800 border border-green-200'
							: 'bg-red-50 text-red-800 border border-red-200'
					)}
				>
					<div className="flex items-center">
						{message.includes('successfully') || message.includes('Welcome') ? (
							<CheckCircle className="w-4 h-4 mr-2" />
						) : (
							<XCircle className="w-4 h-4 mr-2" />
						)}
						{message}
					</div>
				</div>
			)}

			{/* Submit Button */}
			<Button
				type="submit"
				className="w-full"
				size="lg"
				disabled={
					isPending ||
					!emailValidation.isValid ||
					!passwordValidation.isValid ||
					!formData.name.trim() ||
					!formData.role ||
					!formData.acceptTerms ||
					formData.password !== formData.confirmPassword ||
					(['broker', 'developer'].includes(formData.role) && !formData.organizationName.trim())
				}
			>
				{isPending ? (
					<>
						<Loader2 className="w-4 h-4 mr-2 animate-spin" />
						Creating Account...
					</>
				) : (
					'Create Account'
				)}
			</Button>
		</form>
	)
}
