import { z } from 'zod'

// Base email validation
const emailSchema = z
	.string()
	.min(1, 'Email is required')
	.email('Please enter a valid email address')
	.max(255, 'Email must be less than 255 characters')

// Base password validation
const passwordSchema = z
	.string()
	.min(8, 'Password must be at least 8 characters')
	.max(100, 'Password must be less than 100 characters')
	.regex(
		/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
		'Password must contain at least one lowercase letter, one uppercase letter, and one number'
	)

// Sign up schema
export const signUpSchema = z
	.object({
		name: z
			.string()
			.min(1, 'Name is required')
			.min(2, 'Name must be at least 2 characters')
			.max(50, 'Name must be less than 50 characters')
			.regex(/^[a-zA-Z\s]+$/, 'Name can only contain letters and spaces'),
		email: emailSchema,
		password: passwordSchema,
		confirmPassword: z.string().min(1, 'Please confirm your password'),
		role: z.enum(['agent', 'broker', 'developer', 'buyer'], {
			required_error: 'Please select your role'
		}),
		organizationName: z
			.string()
			.min(1, 'Organization name is required')
			.min(2, 'Organization name must be at least 2 characters')
			.max(100, 'Organization name must be less than 100 characters')
			.optional(),
		acceptTerms: z.boolean().refine(val => val === true, 'You must accept the terms and conditions'),
		marketingEmails: z.boolean().optional().default(false)
	})
	.refine(data => data.password === data.confirmPassword, {
		message: 'Passwords do not match',
		path: ['confirmPassword']
	})
	.refine(
		data => {
			// Organization name is required for brokers and developers
			if (['broker', 'developer'].includes(data.role)) {
				return data.organizationName && data.organizationName.length > 0
			}
			return true
		},
		{
			message: 'Organization name is required for brokers and developers',
			path: ['organizationName']
		}
	)

// Sign in schema
export const signInSchema = z.object({
	email: emailSchema,
	password: z.string().min(1, 'Password is required').max(100, 'Password must be less than 100 characters'),
	rememberMe: z.boolean().optional().default(false)
})

// Password reset request schema
export const passwordResetRequestSchema = z.object({
	email: emailSchema
})

// Password reset schema
export const passwordResetSchema = z
	.object({
		token: z.string().min(1, 'Reset token is required'),
		password: passwordSchema,
		confirmPassword: z.string().min(1, 'Please confirm your password')
	})
	.refine(data => data.password === data.confirmPassword, {
		message: 'Passwords do not match',
		path: ['confirmPassword']
	})

// Change password schema
export const changePasswordSchema = z
	.object({
		currentPassword: z
			.string()
			.min(1, 'Current password is required')
			.max(100, 'Password must be less than 100 characters'),
		newPassword: passwordSchema,
		confirmNewPassword: z.string().min(1, 'Please confirm your new password')
	})
	.refine(data => data.newPassword === data.confirmNewPassword, {
		message: 'Passwords do not match',
		path: ['confirmNewPassword']
	})
	.refine(data => data.currentPassword !== data.newPassword, {
		message: 'New password must be different from current password',
		path: ['newPassword']
	})

// Type exports
export type SignUpInput = z.infer<typeof signUpSchema>
export type SignInInput = z.infer<typeof signInSchema>
export type PasswordResetRequestInput = z.infer<typeof passwordResetRequestSchema>
export type PasswordResetInput = z.infer<typeof passwordResetSchema>
export type ChangePasswordInput = z.infer<typeof changePasswordSchema>

// Form field validation helpers
export const validateEmail = (email: string) => {
	try {
		emailSchema.parse(email)
		return { isValid: true, error: null }
	} catch (error) {
		if (error instanceof z.ZodError) {
			return { isValid: false, error: error.errors[0]?.message || 'Invalid email' }
		}
		return { isValid: false, error: 'Invalid email' }
	}
}

export const validatePassword = (password: string) => {
	const strength = getPasswordStrength(password)

	try {
		passwordSchema.parse(password)
		return { isValid: true, error: null, strength: strength.score }
	} catch (error) {
		if (error instanceof z.ZodError) {
			return { isValid: false, error: error.errors[0]?.message || 'Invalid password', strength: strength.score }
		}
		return { isValid: false, error: 'Invalid password', strength: strength.score }
	}
}

// Password strength checker
export const getPasswordStrength = (
	password: string
): {
	score: number
	feedback: string[]
} => {
	const feedback: string[] = []
	let score = 0

	if (password.length >= 8) score += 1
	else feedback.push('Use at least 8 characters')

	if (password.length >= 12) score += 1
	else if (password.length >= 8) feedback.push('Consider using 12+ characters for better security')

	if (/[a-z]/.test(password)) score += 1
	else feedback.push('Include lowercase letters')

	if (/[A-Z]/.test(password)) score += 1
	else feedback.push('Include uppercase letters')

	if (/\d/.test(password)) score += 1
	else feedback.push('Include numbers')

	if (/[^a-zA-Z\d]/.test(password)) score += 1
	else feedback.push('Consider adding special characters')

	return { score, feedback }
}
