import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({
	subsets: ['latin'],
	variable: '--font-inter'
})

export const metadata: Metadata = {
	title: {
		default: 'RealHub - AI-Powered Real Estate Platform',
		template: '%s | RealHub'
	},
	description:
		'Transform your real estate business with AI-powered listings, smart lead qualification, and comprehensive market intelligence. Built for brokers, agents, developers, and buyers.',
	keywords: [
		'real estate',
		'AI',
		'property listings',
		'market intelligence',
		'lead qualification',
		'brokers',
		'agents',
		'developers'
	],
	authors: [{ name: 'RealHub Team' }],
	creator: 'RealHub',
	publisher: 'RealHub',
	formatDetection: {
		email: false,
		address: false,
		telephone: false
	},
	metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'https://realhub.com'),
	alternates: {
		canonical: '/'
	},
	openGraph: {
		type: 'website',
		locale: 'en_US',
		url: '/',
		title: 'RealHub - AI-Powered Real Estate Platform',
		description:
			'Transform your real estate business with AI-powered listings, smart lead qualification, and comprehensive market intelligence.',
		siteName: 'RealHub',
		images: [
			{
				url: '/og-image.jpg',
				width: 1200,
				height: 630,
				alt: 'RealHub - AI-Powered Real Estate Platform'
			}
		]
	},
	twitter: {
		card: 'summary_large_image',
		title: 'RealHub - AI-Powered Real Estate Platform',
		description:
			'Transform your real estate business with AI-powered listings, smart lead qualification, and comprehensive market intelligence.',
		images: ['/og-image.jpg'],
		creator: '@realhub'
	},
	robots: {
		index: true,
		follow: true,
		googleBot: {
			index: true,
			follow: true,
			'max-video-preview': -1,
			'max-image-preview': 'large',
			'max-snippet': -1
		}
	},
	verification: {
		google: process.env.GOOGLE_SITE_VERIFICATION
	}
}

export default function RootLayout({
	children
}: Readonly<{
	children: React.ReactNode
}>) {
	return (
		<html lang="en" className={inter.variable}>
			<head>
				<script
					type="application/ld+json"
					dangerouslySetInnerHTML={{
						__html: JSON.stringify({
							'@context': 'https://schema.org',
							'@type': 'Organization',
							name: 'RealHub',
							description: 'AI-Powered Real Estate Platform',
							url: process.env.NEXT_PUBLIC_APP_URL || 'https://realhub.com',
							logo: `${process.env.NEXT_PUBLIC_APP_URL || 'https://realhub.com'}/logo.png`,
							sameAs: [
								'https://twitter.com/realhub',
								'https://linkedin.com/company/realhub',
								'https://facebook.com/realhub'
							],
							contactPoint: {
								'@type': 'ContactPoint',
								contactType: 'customer service',
								availableLanguage: ['English', 'Tagalog', 'Cebuano']
							}
						})
					}}
				/>
			</head>
			<body className="font-sans antialiased bg-background text-text">{children}</body>
		</html>
	)
}
