'use client'

import { useState, useTransition } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { validateEmail } from '@/lib/validations/auth'
import { Eye, EyeOff, Loader2, CheckCircle, XCircle } from 'lucide-react'
import { cn } from '@/lib/utils'
import { redirectAfterAuth, signInAction } from '@/app/(auth)/_actions/auth'

export function SignInForm() {
	const router = useRouter()
	const [isPending, startTransition] = useTransition()
	const [showPassword, setShowPassword] = useState(false)
	const [formData, setFormData] = useState({
		email: '',
		password: '',
		rememberMe: false
	})
	const [errors, setErrors] = useState<Record<string, string>>({})
	const [message, setMessage] = useState('')
	const [emailValidation, setEmailValidation] = useState<{ isValid: boolean; error: string | null }>({
		isValid: true,
		error: null
	})

	const handleInputChange = (field: string, value: string | boolean) => {
		setFormData(prev => ({ ...prev, [field]: value }))

		// Clear field-specific error
		if (errors[field]) {
			setErrors(prev => ({ ...prev, [field]: '' }))
		}

		// Clear general message
		if (message) {
			setMessage('')
		}

		// Real-time email validation
		if (field === 'email' && typeof value === 'string') {
			const validation = validateEmail(value)
			setEmailValidation(validation)
		}
	}

	const handleSubmit = async (formDataObj: FormData) => {
		startTransition(async () => {
			setErrors({})
			setMessage('')

			try {
				const result = await signInAction(formDataObj)

				if (result.error) {
					setMessage(result.error)
				} else if (result.data) {
					setMessage(result.message || 'Welcome back!')
					// Redirect after successful signin
					setTimeout(() => {
						redirectAfterAuth(result.data.user.role)
					}, 1500)
				}
			} catch (error) {
				console.error('Signin error:', error)
				setMessage('Something went wrong. Please try again.')
			}
		})
	}

	return (
		<form action={handleSubmit} className="space-y-6">
			{/* Email Field */}
			<div>
				<Label htmlFor="email" className="block text-sm font-medium text-text mb-2">
					Email Address
				</Label>
				<Input
					id="email"
					name="email"
					type="email"
					required
					value={formData.email}
					onChange={e => handleInputChange('email', e.target.value)}
					className={cn(
						(errors.email || !emailValidation.isValid) && 'border-red-500 focus:ring-red-500',
						emailValidation.isValid && formData.email && 'border-green-500 focus:ring-green-500'
					)}
					placeholder="Enter your email address"
					disabled={isPending}
					autoComplete="email"
				/>
				{!emailValidation.isValid && emailValidation.error && (
					<p className="mt-1 text-sm text-red-600 flex items-center">
						<XCircle className="w-4 h-4 mr-1" />
						{emailValidation.error}
					</p>
				)}
				{emailValidation.isValid && formData.email && (
					<p className="mt-1 text-sm text-green-600 flex items-center">
						<CheckCircle className="w-4 h-4 mr-1" />
						Valid email address
					</p>
				)}
			</div>

			{/* Password Field */}
			<div>
				<Label htmlFor="password" className="block text-sm font-medium text-text mb-2">
					Password
				</Label>
				<div className="relative">
					<Input
						id="password"
						name="password"
						type={showPassword ? 'text' : 'password'}
						required
						value={formData.password}
						onChange={e => handleInputChange('password', e.target.value)}
						className={cn(errors.password && 'border-red-500 focus:ring-red-500', 'pr-10')}
						placeholder="Enter your password"
						disabled={isPending}
						autoComplete="current-password"
					/>
					<button
						type="button"
						onClick={() => setShowPassword(!showPassword)}
						className="absolute inset-y-0 right-0 pr-3 flex items-center text-text-soft hover:text-text focus:outline-none focus:ring-2 focus:ring-primary rounded"
						disabled={isPending}
						aria-label={showPassword ? 'Hide password' : 'Show password'}
					>
						{showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
					</button>
				</div>
				{errors.password && (
					<p className="mt-1 text-sm text-red-600 flex items-center">
						<XCircle className="w-4 h-4 mr-1" />
						{errors.password}
					</p>
				)}
			</div>

			{/* Remember Me */}
			<div className="flex items-center justify-between">
				<div className="flex items-center">
					<input
						id="rememberMe"
						name="rememberMe"
						type="checkbox"
						checked={formData.rememberMe}
						onChange={e => handleInputChange('rememberMe', e.target.checked)}
						className="h-4 w-4 text-primary focus:ring-primary border-border rounded"
						disabled={isPending}
					/>
					<Label htmlFor="rememberMe" className="ml-2 text-sm text-text-soft">
						Remember me for 30 days
					</Label>
				</div>
			</div>

			{/* Error/Success Message */}
			{message && (
				<div
					className={cn(
						'p-4 rounded-lg text-sm',
						message.includes('Welcome') || message.includes('successfully')
							? 'bg-green-50 text-green-800 border border-green-200'
							: 'bg-red-50 text-red-800 border border-red-200'
					)}
				>
					<div className="flex items-center">
						{message.includes('Welcome') || message.includes('successfully') ? (
							<CheckCircle className="w-4 h-4 mr-2" />
						) : (
							<XCircle className="w-4 h-4 mr-2" />
						)}
						{message}
					</div>
				</div>
			)}

			{/* Submit Button */}
			<Button
				type="submit"
				className="w-full"
				size="lg"
				disabled={isPending || !emailValidation.isValid || !formData.email || !formData.password}
			>
				{isPending ? (
					<>
						<Loader2 className="w-4 h-4 mr-2 animate-spin" />
						Signing In...
					</>
				) : (
					'Sign In'
				)}
			</Button>

			{/* Additional Help */}
			<div className="text-center">
				<p className="text-sm text-text-soft">
					Having trouble signing in?{' '}
					<a
						href="/contact"
						className="text-primary hover:text-primary-hover focus:outline-none focus:ring-2 focus:ring-primary rounded px-1"
					>
						Contact support
					</a>
				</p>
			</div>
		</form>
	)
}
