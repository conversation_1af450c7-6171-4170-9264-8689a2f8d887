import { Metadata } from 'next'
import Link from 'next/link'
import { SignInForm } from '../_components/signin-form'
import { Home } from 'lucide-react'

export const metadata: Metadata = {
  title: 'Sign In - RealHub',
  description: 'Sign in to your RealHub account and access your AI-powered real estate platform.',
  robots: {
    index: false,
    follow: false,
  },
}

export default function SignInPage() {
  return (
    <div className="min-h-screen bg-background flex flex-col">
      {/* Header */}
      <header className="border-b border-border bg-card/50 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center space-x-2 focus:outline-none focus:ring-2 focus:ring-primary rounded-lg p-1">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <Home className="w-5 h-5 text-text" />
              </div>
              <span className="text-xl font-bold text-text">RealHub</span>
            </Link>
            <div className="text-sm text-text-soft">
              Don't have an account?{' '}
              <Link 
                href="/auth/signup" 
                className="text-primary hover:text-primary-hover font-medium focus:outline-none focus:ring-2 focus:ring-primary rounded px-1"
              >
                Sign up
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-text mb-2">
              Welcome back
            </h1>
            <p className="text-text-soft">
              Sign in to your RealHub account to continue
            </p>
          </div>

          <SignInForm />

          <div className="text-center space-y-4">
            <Link 
              href="/auth/forgot-password" 
              className="text-sm text-primary hover:text-primary-hover focus:outline-none focus:ring-2 focus:ring-primary rounded px-1"
            >
              Forgot your password?
            </Link>
            
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-border" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-background text-text-soft">Or continue with</span>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <button
                type="button"
                className="w-full inline-flex justify-center py-2 px-4 border border-border rounded-lg shadow-sm bg-card text-sm font-medium text-text hover:bg-border/10 focus:outline-none focus:ring-2 focus:ring-primary"
                disabled
              >
                <svg className="w-5 h-5" viewBox="0 0 24 24">
                  <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                  <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                  <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                  <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                <span className="ml-2">Google</span>
              </button>

              <button
                type="button"
                className="w-full inline-flex justify-center py-2 px-4 border border-border rounded-lg shadow-sm bg-card text-sm font-medium text-text hover:bg-border/10 focus:outline-none focus:ring-2 focus:ring-primary"
                disabled
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
                <span className="ml-2">Facebook</span>
              </button>
            </div>

            <p className="text-xs text-text-soft">
              OAuth integration coming soon
            </p>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="border-t border-border bg-card/50 py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
            <p className="text-sm text-text-soft">
              © 2024 RealHub. All rights reserved.
            </p>
            <div className="flex space-x-6">
              <Link 
                href="/help" 
                className="text-sm text-text-soft hover:text-text focus:outline-none focus:ring-2 focus:ring-primary rounded px-1"
              >
                Help
              </Link>
              <Link 
                href="/contact" 
                className="text-sm text-text-soft hover:text-text focus:outline-none focus:ring-2 focus:ring-primary rounded px-1"
              >
                Contact
              </Link>
              <Link 
                href="/status" 
                className="text-sm text-text-soft hover:text-text focus:outline-none focus:ring-2 focus:ring-primary rounded px-1"
              >
                Status
              </Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
