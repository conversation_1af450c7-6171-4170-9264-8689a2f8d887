import { NextRequest, NextResponse } from 'next/server'
import bcrypt from 'bcryptjs'
import { signInSchema } from '@/lib/validations/auth'
import type { APIResponse } from '@/lib/types'
import { z } from 'zod'

// Rate limiting (in production, use Redis or similar)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>()

function rateLimit(ip: string, limit: number = 10, windowMs: number = 15 * 60 * 1000): boolean {
  const now = Date.now()
  const record = rateLimitMap.get(ip)

  if (!record || now > record.resetTime) {
    rateLimitMap.set(ip, { count: 1, resetTime: now + windowMs })
    return true
  }

  if (record.count >= limit) {
    return false
  }

  record.count++
  return true
}

// Mock database operations (replace with actual Prisma calls)
const mockUsers: Array<{
  id: string
  email: string
  name: string
  passwordHash: string
  role: string
  organizationId?: string
  createdAt: Date
}> = []

// Helper function to generate session token (replace with proper JWT or session management)
const generateSessionToken = () => Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)

export async function POST(request: NextRequest): Promise<NextResponse<APIResponse>> {
  try {
    // Rate limiting
    const ip = request.ip || request.headers.get('x-forwarded-for') || 'unknown'
    if (!rateLimit(ip)) {
      return NextResponse.json(
        { error: 'Too many sign-in attempts. Please try again later.' },
        { status: 429 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validatedData = signInSchema.parse(body)

    // Find user by email
    const user = mockUsers.find(u => u.email === validatedData.email)
    if (!user) {
      // Use generic error message to prevent email enumeration
      return NextResponse.json(
        { error: 'Invalid email or password. Please check your credentials and try again.' },
        { status: 401 }
      )
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(validatedData.password, user.passwordHash)
    if (!isValidPassword) {
      return NextResponse.json(
        { error: 'Invalid email or password. Please check your credentials and try again.' },
        { status: 401 }
      )
    }

    // Generate session token (in production, use proper JWT with expiration)
    const sessionToken = generateSessionToken()
    const expiresAt = new Date(Date.now() + (validatedData.rememberMe ? 30 * 24 * 60 * 60 * 1000 : 24 * 60 * 60 * 1000)) // 30 days or 1 day

    // Return success response with session token
    const response: APIResponse = {
      data: {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          organizationId: user.organizationId,
          createdAt: user.createdAt,
        },
        session: {
          token: sessionToken,
          expiresAt: expiresAt.toISOString(),
        },
      },
      message: 'Welcome back! You have been signed in successfully.',
    }

    // Set secure HTTP-only cookie for web clients
    const nextResponse = NextResponse.json(response, { status: 200 })
    nextResponse.cookies.set('session-token', sessionToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      expires: expiresAt,
      path: '/',
    })

    return nextResponse
  } catch (error) {
    console.error('API Signin error:', error)

    if (error instanceof z.ZodError) {
      const firstError = error.errors[0]
      return NextResponse.json(
        { error: firstError?.message || 'Invalid input data.' },
        { status: 400 }
      )
    }

    if (error instanceof SyntaxError) {
      return NextResponse.json(
        { error: 'Invalid JSON format.' },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error. Please try again later.' },
      { status: 500 }
    )
  }
}

// Handle unsupported methods
export async function GET(): Promise<NextResponse> {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to sign in.' },
    { status: 405 }
  )
}

export async function PUT(): Promise<NextResponse> {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to sign in.' },
    { status: 405 }
  )
}

export async function DELETE(): Promise<NextResponse> {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to sign in.' },
    { status: 405 }
  )
}

export async function PATCH(): Promise<NextResponse> {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to sign in.' },
    { status: 405 }
  )
}
