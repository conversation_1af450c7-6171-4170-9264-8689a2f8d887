import { Metada<PERSON> } from 'next'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import {
	Brain,
	Building2,
	Users,
	TrendingUp,
	Shield,
	Globe,
	Zap,
	BarChart3,
	Home,
	UserCheck,
	MessageSquare,
	Star
} from 'lucide-react'

export const metadata: Metadata = {
	title: 'RealHub - AI-Powered Real Estate Platform',
	description:
		'Transform your real estate business with AI-powered listings, smart lead qualification, and comprehensive market intelligence. Built for brokers, agents, developers, and buyers.',
	openGraph: {
		title: 'RealHub - AI-Powered Real Estate Platform',
		description:
			'Transform your real estate business with AI-powered listings, smart lead qualification, and comprehensive market intelligence.',
		type: 'website',
		url: '/'
	}
}

export default function HomePage() {
	return (
		<>
			{/* JSON-LD Structured Data */}
			<script
				type="application/ld+json"
				dangerouslySetInnerHTML={{
					__html: JSON.stringify({
						'@context': 'https://schema.org',
						'@type': 'WebSite',
						name: 'RealHub',
						description: 'AI-Powered Real Estate Platform',
						url: process.env.NEXT_PUBLIC_APP_URL || 'https://realhub.com',
						potentialAction: {
							'@type': 'SearchAction',
							target: `${process.env.NEXT_PUBLIC_APP_URL || 'https://realhub.com'}/search?q={search_term_string}`,
							'query-input': 'required name=search_term_string'
						},
						publisher: {
							'@type': 'Organization',
							name: 'RealHub',
							logo: `${process.env.NEXT_PUBLIC_APP_URL || 'https://realhub.com'}/logo.png`
						}
					})
				}}
			/>

			<div className="min-h-screen bg-background">
				{/* Navigation */}
				<nav
					className="border-b border-border bg-card/50 backdrop-blur-sm sticky top-0 z-50"
					role="navigation"
					aria-label="Main navigation"
				>
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="flex justify-between items-center h-16">
							<div className="flex items-center">
								<Link
									href="/"
									className="flex items-center space-x-2 focus:outline-none focus:ring-2 focus:ring-primary rounded-lg p-1"
								>
									<div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
										<Home className="w-5 h-5 text-text" />
									</div>
									<span className="text-xl font-bold text-text">RealHub</span>
								</Link>
							</div>
							<div className="hidden md:flex items-center space-x-8">
								<Link
									href="/features"
									className="text-text-soft hover:text-text transition-colors focus:outline-none focus:ring-2 focus:ring-primary rounded px-2 py-1"
								>
									Features
								</Link>
								<Link
									href="/pricing"
									className="text-text-soft hover:text-text transition-colors focus:outline-none focus:ring-2 focus:ring-primary rounded px-2 py-1"
								>
									Pricing
								</Link>
								<Link
									href="/about"
									className="text-text-soft hover:text-text transition-colors focus:outline-none focus:ring-2 focus:ring-primary rounded px-2 py-1"
								>
									About
								</Link>
								<Link
									href="/contact"
									className="text-text-soft hover:text-text transition-colors focus:outline-none focus:ring-2 focus:ring-primary rounded px-2 py-1"
								>
									Contact
								</Link>
							</div>
							<div className="flex items-center space-x-4">
								<Link href="/auth/signin">
									<Button variant="ghost" size="sm">
										Sign In
									</Button>
								</Link>
								<Link href="/auth/signup">
									<Button size="sm">Get Started</Button>
								</Link>
							</div>
						</div>
					</div>
				</nav>

				{/* Hero Section */}
				<section
					className="relative overflow-hidden bg-gradient-to-br from-background via-card to-background py-20 sm:py-32"
					aria-labelledby="hero-heading"
				>
					<div className="absolute inset-0 bg-[url('/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))]"></div>
					<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center">
							<h1 id="hero-heading" className="text-4xl sm:text-6xl lg:text-7xl font-bold text-text mb-6 leading-tight">
								AI-Powered Real Estate
								<span className="block text-primary">Platform</span>
							</h1>
							<p className="text-xl sm:text-2xl text-text-soft mb-8 max-w-3xl mx-auto leading-relaxed">
								Transform your real estate business with intelligent listings, smart lead qualification, and
								comprehensive market intelligence. Built for the modern real estate professional.
							</p>
							<div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
								<Link href="/auth/signup">
									<Button size="lg" className="w-full sm:w-auto">
										Start Free Trial
										<Zap className="ml-2 h-5 w-5" />
									</Button>
								</Link>
								<Link href="/demo">
									<Button variant="outline" size="lg" className="w-full sm:w-auto">
										Watch Demo
									</Button>
								</Link>
							</div>
							<p className="text-sm text-secondary mt-4">
								No credit card required • 14-day free trial • Cancel anytime
							</p>
						</div>
					</div>
				</section>

				{/* Features Section */}
				<section className="py-20 bg-card" aria-labelledby="features-heading">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-16">
							<h2 id="features-heading" className="text-3xl sm:text-4xl font-bold text-text mb-4">
								Powered by Advanced AI Technology
							</h2>
							<p className="text-xl text-text-soft max-w-2xl mx-auto">
								Leverage cutting-edge artificial intelligence to streamline your real estate operations and deliver
								exceptional results for your clients.
							</p>
						</div>

						<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
							<div className="bg-background p-8 rounded-2xl border border-border hover:shadow-lg transition-shadow">
								<div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-6">
									<Brain className="w-6 h-6 text-primary" />
								</div>
								<h3 className="text-xl font-semibold text-text mb-4">Smart Listing Generation</h3>
								<p className="text-text-soft">
									AI automatically generates compelling property descriptions, suggests optimal pricing, and identifies
									key selling points to maximize listing performance.
								</p>
							</div>

							<div className="bg-background p-8 rounded-2xl border border-border hover:shadow-lg transition-shadow">
								<div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-6">
									<UserCheck className="w-6 h-6 text-primary" />
								</div>
								<h3 className="text-xl font-semibold text-text mb-4">Lead Qualification</h3>
								<p className="text-text-soft">
									Intelligent lead scoring and qualification helps you focus on the most promising prospects, increasing
									conversion rates and saving valuable time.
								</p>
							</div>

							<div className="bg-background p-8 rounded-2xl border border-border hover:shadow-lg transition-shadow">
								<div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-6">
									<BarChart3 className="w-6 h-6 text-primary" />
								</div>
								<h3 className="text-xl font-semibold text-text mb-4">Market Intelligence</h3>
								<p className="text-text-soft">
									Real-time market analysis, price predictions, and trend insights help you make data-driven decisions
									and stay ahead of the competition.
								</p>
							</div>

							<div className="bg-background p-8 rounded-2xl border border-border hover:shadow-lg transition-shadow">
								<div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-6">
									<MessageSquare className="w-6 h-6 text-primary" />
								</div>
								<h3 className="text-xl font-semibold text-text mb-4">Automated Communication</h3>
								<p className="text-text-soft">
									AI-powered chatbots and email automation ensure no lead goes unattended, providing instant responses
									and nurturing prospects 24/7.
								</p>
							</div>

							<div className="bg-background p-8 rounded-2xl border border-border hover:shadow-lg transition-shadow">
								<div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-6">
									<Globe className="w-6 h-6 text-primary" />
								</div>
								<h3 className="text-xl font-semibold text-text mb-4">Multilingual Support</h3>
								<p className="text-text-soft">
									Native support for English, Tagalog, and Cebuano ensures you can serve diverse markets and connect
									with clients in their preferred language.
								</p>
							</div>

							<div className="bg-background p-8 rounded-2xl border border-border hover:shadow-lg transition-shadow">
								<div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-6">
									<Shield className="w-6 h-6 text-primary" />
								</div>
								<h3 className="text-xl font-semibold text-text mb-4">Enterprise Security</h3>
								<p className="text-text-soft">
									Bank-level security with role-based access control, data encryption, and compliance with industry
									standards to protect your business.
								</p>
							</div>
						</div>
					</div>
				</section>

				{/* User Types Section */}
				<section className="py-20 bg-background" aria-labelledby="user-types-heading">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-16">
							<h2 id="user-types-heading" className="text-3xl sm:text-4xl font-bold text-text mb-4">
								Built for Every Real Estate Professional
							</h2>
							<p className="text-xl text-text-soft max-w-2xl mx-auto">
								Whether you're a broker, agent, developer, or buyer, RealHub adapts to your unique needs and workflow
								requirements.
							</p>
						</div>

						<div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
							<div className="bg-card p-8 rounded-2xl border border-border">
								<div className="flex items-center mb-6">
									<div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mr-4">
										<Building2 className="w-6 h-6 text-primary" />
									</div>
									<h3 className="text-2xl font-semibold text-text">For Brokers & Agencies</h3>
								</div>
								<ul className="space-y-4 text-text-soft">
									<li className="flex items-start">
										<Star className="w-5 h-5 text-primary mt-0.5 mr-3 flex-shrink-0" />
										<span>Manage multiple agents and track team performance with comprehensive analytics</span>
									</li>
									<li className="flex items-start">
										<Star className="w-5 h-5 text-primary mt-0.5 mr-3 flex-shrink-0" />
										<span>Centralized lead distribution and automated assignment based on agent expertise</span>
									</li>
									<li className="flex items-start">
										<Star className="w-5 h-5 text-primary mt-0.5 mr-3 flex-shrink-0" />
										<span>White-label solutions with custom branding and domain configuration</span>
									</li>
									<li className="flex items-start">
										<Star className="w-5 h-5 text-primary mt-0.5 mr-3 flex-shrink-0" />
										<span>Advanced reporting and commission tracking for streamlined operations</span>
									</li>
								</ul>
							</div>

							<div className="bg-card p-8 rounded-2xl border border-border">
								<div className="flex items-center mb-6">
									<div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mr-4">
										<Users className="w-6 h-6 text-primary" />
									</div>
									<h3 className="text-2xl font-semibold text-text">For Individual Agents</h3>
								</div>
								<ul className="space-y-4 text-text-soft">
									<li className="flex items-start">
										<Star className="w-5 h-5 text-primary mt-0.5 mr-3 flex-shrink-0" />
										<span>Personal CRM with AI-powered lead scoring and follow-up reminders</span>
									</li>
									<li className="flex items-start">
										<Star className="w-5 h-5 text-primary mt-0.5 mr-3 flex-shrink-0" />
										<span>Professional listing websites with SEO optimization and social sharing</span>
									</li>
									<li className="flex items-start">
										<Star className="w-5 h-5 text-primary mt-0.5 mr-3 flex-shrink-0" />
										<span>Mobile app for on-the-go property management and client communication</span>
									</li>
									<li className="flex items-start">
										<Star className="w-5 h-5 text-primary mt-0.5 mr-3 flex-shrink-0" />
										<span>Automated market reports and comparative market analysis tools</span>
									</li>
								</ul>
							</div>

							<div className="bg-card p-8 rounded-2xl border border-border">
								<div className="flex items-center mb-6">
									<div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mr-4">
										<TrendingUp className="w-6 h-6 text-primary" />
									</div>
									<h3 className="text-2xl font-semibold text-text">For Developers</h3>
								</div>
								<ul className="space-y-4 text-text-soft">
									<li className="flex items-start">
										<Star className="w-5 h-5 text-primary mt-0.5 mr-3 flex-shrink-0" />
										<span>Project management tools for tracking construction progress and sales</span>
									</li>
									<li className="flex items-start">
										<Star className="w-5 h-5 text-primary mt-0.5 mr-3 flex-shrink-0" />
										<span>Pre-construction sales management with reservation and payment tracking</span>
									</li>
									<li className="flex items-start">
										<Star className="w-5 h-5 text-primary mt-0.5 mr-3 flex-shrink-0" />
										<span>Market demand analysis and pricing optimization for new developments</span>
									</li>
									<li className="flex items-start">
										<Star className="w-5 h-5 text-primary mt-0.5 mr-3 flex-shrink-0" />
										<span>Investor relations portal with progress updates and financial reporting</span>
									</li>
								</ul>
							</div>

							<div className="bg-card p-8 rounded-2xl border border-border">
								<div className="flex items-center mb-6">
									<div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mr-4">
										<Home className="w-6 h-6 text-primary" />
									</div>
									<h3 className="text-2xl font-semibold text-text">For Buyers</h3>
								</div>
								<ul className="space-y-4 text-text-soft">
									<li className="flex items-start">
										<Star className="w-5 h-5 text-primary mt-0.5 mr-3 flex-shrink-0" />
										<span>AI-powered property recommendations based on preferences and budget</span>
									</li>
									<li className="flex items-start">
										<Star className="w-5 h-5 text-primary mt-0.5 mr-3 flex-shrink-0" />
										<span>Virtual tours and 3D property visualization for remote viewing</span>
									</li>
									<li className="flex items-start">
										<Star className="w-5 h-5 text-primary mt-0.5 mr-3 flex-shrink-0" />
										<span>Mortgage calculator and financing options with pre-approval assistance</span>
									</li>
									<li className="flex items-start">
										<Star className="w-5 h-5 text-primary mt-0.5 mr-3 flex-shrink-0" />
										<span>Neighborhood insights including schools, amenities, and market trends</span>
									</li>
								</ul>
							</div>
						</div>
					</div>
				</section>

				{/* CTA Section */}
				<section
					className="py-20 bg-gradient-to-r from-primary/10 via-primary/5 to-primary/10"
					aria-labelledby="cta-heading"
				>
					<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
						<h2 id="cta-heading" className="text-3xl sm:text-4xl font-bold text-text mb-6">
							Ready to Transform Your Real Estate Business?
						</h2>
						<p className="text-xl text-text-soft mb-8 max-w-2xl mx-auto">
							Join thousands of real estate professionals who are already using RealHub to streamline their operations
							and grow their business with AI.
						</p>
						<div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
							<Link href="/auth/signup">
								<Button size="lg" className="w-full sm:w-auto">
									Start Your Free Trial
									<Zap className="ml-2 h-5 w-5" />
								</Button>
							</Link>
							<Link href="/contact">
								<Button variant="outline" size="lg" className="w-full sm:w-auto">
									Schedule Demo
								</Button>
							</Link>
						</div>
						<p className="text-sm text-secondary mt-6">14-day free trial • No setup fees • Cancel anytime</p>
					</div>
				</section>

				{/* Footer */}
				<footer className="bg-text text-card py-16" role="contentinfo">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
							<div className="lg:col-span-1">
								<div className="flex items-center space-x-2 mb-4">
									<div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
										<Home className="w-5 h-5 text-text" />
									</div>
									<span className="text-xl font-bold">RealHub</span>
								</div>
								<p className="text-card/70 mb-4">
									AI-powered real estate platform transforming how professionals manage listings, leads, and market
									intelligence.
								</p>
								<div className="flex space-x-4">
									<a
										href="#"
										className="text-card/70 hover:text-card transition-colors focus:outline-none focus:ring-2 focus:ring-primary rounded"
										aria-label="Twitter"
									>
										<svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
											<path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
										</svg>
									</a>
									<a
										href="#"
										className="text-card/70 hover:text-card transition-colors focus:outline-none focus:ring-2 focus:ring-primary rounded"
										aria-label="LinkedIn"
									>
										<svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
											<path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
										</svg>
									</a>
									<a
										href="#"
										className="text-card/70 hover:text-card transition-colors focus:outline-none focus:ring-2 focus:ring-primary rounded"
										aria-label="Facebook"
									>
										<svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
											<path
												fillRule="evenodd"
												d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
												clipRule="evenodd"
											/>
										</svg>
									</a>
								</div>
							</div>

							<div>
								<h3 className="text-lg font-semibold mb-4">Product</h3>
								<ul className="space-y-2">
									<li>
										<Link
											href="/features"
											className="text-card/70 hover:text-card transition-colors focus:outline-none focus:ring-2 focus:ring-primary rounded"
										>
											Features
										</Link>
									</li>
									<li>
										<Link
											href="/pricing"
											className="text-card/70 hover:text-card transition-colors focus:outline-none focus:ring-2 focus:ring-primary rounded"
										>
											Pricing
										</Link>
									</li>
									<li>
										<Link
											href="/integrations"
											className="text-card/70 hover:text-card transition-colors focus:outline-none focus:ring-2 focus:ring-primary rounded"
										>
											Integrations
										</Link>
									</li>
									<li>
										<Link
											href="/api"
											className="text-card/70 hover:text-card transition-colors focus:outline-none focus:ring-2 focus:ring-primary rounded"
										>
											API
										</Link>
									</li>
								</ul>
							</div>

							<div>
								<h3 className="text-lg font-semibold mb-4">Company</h3>
								<ul className="space-y-2">
									<li>
										<Link
											href="/about"
											className="text-card/70 hover:text-card transition-colors focus:outline-none focus:ring-2 focus:ring-primary rounded"
										>
											About
										</Link>
									</li>
									<li>
										<Link
											href="/careers"
											className="text-card/70 hover:text-card transition-colors focus:outline-none focus:ring-2 focus:ring-primary rounded"
										>
											Careers
										</Link>
									</li>
									<li>
										<Link
											href="/blog"
											className="text-card/70 hover:text-card transition-colors focus:outline-none focus:ring-2 focus:ring-primary rounded"
										>
											Blog
										</Link>
									</li>
									<li>
										<Link
											href="/contact"
											className="text-card/70 hover:text-card transition-colors focus:outline-none focus:ring-2 focus:ring-primary rounded"
										>
											Contact
										</Link>
									</li>
								</ul>
							</div>

							<div>
								<h3 className="text-lg font-semibold mb-4">Support</h3>
								<ul className="space-y-2">
									<li>
										<Link
											href="/help"
											className="text-card/70 hover:text-card transition-colors focus:outline-none focus:ring-2 focus:ring-primary rounded"
										>
											Help Center
										</Link>
									</li>
									<li>
										<Link
											href="/docs"
											className="text-card/70 hover:text-card transition-colors focus:outline-none focus:ring-2 focus:ring-primary rounded"
										>
											Documentation
										</Link>
									</li>
									<li>
										<Link
											href="/status"
											className="text-card/70 hover:text-card transition-colors focus:outline-none focus:ring-2 focus:ring-primary rounded"
										>
											Status
										</Link>
									</li>
									<li>
										<Link
											href="/privacy"
											className="text-card/70 hover:text-card transition-colors focus:outline-none focus:ring-2 focus:ring-primary rounded"
										>
											Privacy
										</Link>
									</li>
								</ul>
							</div>
						</div>

						<div className="border-t border-card/20 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
							<p className="text-card/70 text-sm">© 2024 RealHub. All rights reserved.</p>
							<div className="flex space-x-6 mt-4 md:mt-0">
								<Link
									href="/terms"
									className="text-card/70 hover:text-card text-sm transition-colors focus:outline-none focus:ring-2 focus:ring-primary rounded"
								>
									Terms of Service
								</Link>
								<Link
									href="/privacy"
									className="text-card/70 hover:text-card text-sm transition-colors focus:outline-none focus:ring-2 focus:ring-primary rounded"
								>
									Privacy Policy
								</Link>
								<Link
									href="/cookies"
									className="text-card/70 hover:text-card text-sm transition-colors focus:outline-none focus:ring-2 focus:ring-primary rounded"
								>
									Cookie Policy
								</Link>
							</div>
						</div>
					</div>
				</footer>
			</div>
		</>
	)
}
