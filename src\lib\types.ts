export interface User {
  id: string
  email: string
  name?: string
  role: UserRole
  organizationId?: string
  createdAt: Date
  updatedAt: Date
}

export type UserRole = 'owner' | 'admin' | 'agent' | 'broker' | 'developer' | 'buyer'

export interface Organization {
  id: string
  name: string
  plan: 'free' | 'pro' | 'enterprise'
  settings: OrganizationSettings
  createdAt: Date
  updatedAt: Date
}

export interface OrganizationSettings {
  allowPublicListings: boolean
  requireApproval: boolean
  defaultLanguage: 'en' | 'tl' | 'ceb'
  timezone: string
}

export interface Listing {
  id: string
  organizationId: string
  title: string
  description: string
  price: number
  beds: number
  baths: number
  sqft?: number
  address: string
  city: string
  state: string
  zipCode: string
  country: string
  latitude?: number
  longitude?: number
  status: 'draft' | 'active' | 'pending' | 'sold' | 'archived'
  type: 'house' | 'apartment' | 'condo' | 'townhouse' | 'land' | 'commercial'
  images: string[]
  features: string[]
  agentId?: string
  createdAt: Date
  updatedAt: Date
  seo?: SEOOverride
}

export interface Agent {
  id: string
  userId?: string
  name: string
  email: string
  phone?: string
  bio?: string
  avatar?: string
  licenseNumber?: string
  organizationId: string
  specialties: string[]
  languages: string[]
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface Lead {
  id: string
  name: string
  email: string
  phone?: string
  message?: string
  source: 'website' | 'referral' | 'social' | 'advertising' | 'other'
  status: 'new' | 'contacted' | 'qualified' | 'converted' | 'lost'
  listingId?: string
  agentId?: string
  organizationId: string
  aiScore?: number
  aiInsights?: string
  createdAt: Date
  updatedAt: Date
}

export interface SEOSettings {
  id: string
  organizationId: string
  siteTitle: string
  siteDescription: string
  defaultOGImage?: string
  twitterHandle?: string
  googleAnalyticsId?: string
  googleTagManagerId?: string
  facebookPixelId?: string
  robotsAllowIndexing: boolean
  canonicalBaseUrl: string
  defaultLanguage: string
  supportedLanguages: string[]
  createdAt: Date
  updatedAt: Date
}

export interface SEOOverride {
  title?: string
  description?: string
  ogImage?: string
  noIndex?: boolean
  noFollow?: boolean
  canonicalUrl?: string
}

export interface APIResponse<T = any> {
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T = any> extends APIResponse<T[]> {
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export interface SearchFilters {
  query?: string
  minPrice?: number
  maxPrice?: number
  beds?: number
  baths?: number
  type?: Listing['type']
  city?: string
  state?: string
  features?: string[]
  sortBy?: 'price' | 'date' | 'beds' | 'baths' | 'sqft'
  sortOrder?: 'asc' | 'desc'
}
