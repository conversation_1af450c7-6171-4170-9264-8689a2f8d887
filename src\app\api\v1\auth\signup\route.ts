import { NextRequest, NextResponse } from 'next/server'
import bcrypt from 'bcryptjs'
import { signUpSchema } from '@/lib/validations/auth'
import type { APIResponse } from '@/lib/types'
import { z } from 'zod'

// Rate limiting (in production, use Redis or similar)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>()

function rateLimit(ip: string, limit: number = 5, windowMs: number = 15 * 60 * 1000): boolean {
  const now = Date.now()
  const record = rateLimitMap.get(ip)

  if (!record || now > record.resetTime) {
    rateLimitMap.set(ip, { count: 1, resetTime: now + windowMs })
    return true
  }

  if (record.count >= limit) {
    return false
  }

  record.count++
  return true
}

// Mock database operations (replace with actual Prisma calls)
const mockUsers: Array<{
  id: string
  email: string
  name: string
  passwordHash: string
  role: string
  organizationId?: string
  createdAt: Date
}> = []

const mockOrganizations: Array<{
  id: string
  name: string
  plan: string
  createdAt: Date
}> = []

// Helper function to generate IDs (replace with proper UUID generation)
const generateId = () => Math.random().toString(36).substring(2, 15)

export async function POST(request: NextRequest): Promise<NextResponse<APIResponse>> {
  try {
    // Rate limiting
    const ip = request.ip || request.headers.get('x-forwarded-for') || 'unknown'
    if (!rateLimit(ip)) {
      return NextResponse.json(
        { error: 'Too many requests. Please try again later.' },
        { status: 429 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validatedData = signUpSchema.parse(body)

    // Check if user already exists
    const existingUser = mockUsers.find(user => user.email === validatedData.email)
    if (existingUser) {
      return NextResponse.json(
        { error: 'An account with this email already exists. Please sign in instead.' },
        { status: 409 }
      )
    }

    // Hash password
    const passwordHash = await bcrypt.hash(validatedData.password, 12)

    // Create organization if needed
    let organizationId: string | undefined
    if (validatedData.organizationName && ['broker', 'developer'].includes(validatedData.role)) {
      const organization = {
        id: generateId(),
        name: validatedData.organizationName,
        plan: 'free' as const,
        createdAt: new Date(),
      }
      mockOrganizations.push(organization)
      organizationId = organization.id
    }

    // Create user
    const user = {
      id: generateId(),
      email: validatedData.email,
      name: validatedData.name,
      passwordHash,
      role: validatedData.role,
      organizationId,
      createdAt: new Date(),
    }
    mockUsers.push(user)

    // Return success response (exclude sensitive data)
    const response: APIResponse = {
      data: {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          organizationId: user.organizationId,
          createdAt: user.createdAt,
        },
      },
      message: 'Account created successfully! Welcome to RealHub.',
    }

    return NextResponse.json(response, { status: 201 })
  } catch (error) {
    console.error('API Signup error:', error)

    if (error instanceof z.ZodError) {
      const firstError = error.errors[0]
      return NextResponse.json(
        { error: firstError?.message || 'Invalid input data.' },
        { status: 400 }
      )
    }

    if (error instanceof SyntaxError) {
      return NextResponse.json(
        { error: 'Invalid JSON format.' },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error. Please try again later.' },
      { status: 500 }
    )
  }
}

// Handle unsupported methods
export async function GET(): Promise<NextResponse> {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to create an account.' },
    { status: 405 }
  )
}

export async function PUT(): Promise<NextResponse> {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to create an account.' },
    { status: 405 }
  )
}

export async function DELETE(): Promise<NextResponse> {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to create an account.' },
    { status: 405 }
  )
}

export async function PATCH(): Promise<NextResponse> {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to create an account.' },
    { status: 405 }
  )
}
